import { useState, useEffect } from 'react';
import { NavLink, Link } from 'react-router-dom';
import { Menu, X } from 'lucide-react';
import Logo from './Logo';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Close mobile menu when route changes or on resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024 && isMenuOpen) {
        setIsMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isMenuOpen]);

  // Lock body scroll when mobile menu is open and prevent horizontal scrollbars
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
      document.body.classList.add('mobile-menu-open');
    } else {
      document.body.style.overflow = 'unset';
      document.body.classList.remove('mobile-menu-open');
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
      document.body.classList.remove('mobile-menu-open');
    };
  }, [isMenuOpen]);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  return (
    <>
      <header className="absolute top-0 z-50 w-full h-14 sm:h-16 md:h-18 lg:h-20 flex items-center will-change-transform bg-transparent">
        <div className="container mx-auto px-4 flex items-center justify-between">
          <Link to="/" className="flex items-center" aria-label="Amara Nursing Home Page">
            <Logo className="h-8 sm:h-9 md:h-10 lg:h-11 w-auto" />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            <NavLink to="/" className={({ isActive }) =>
              `text-sm font-semibold transition-colors px-1 py-1 ${isActive ? 'text-white border-b-2 border-white' : 'text-white hover:text-white/80'}`
            }>
              Home
            </NavLink>
            <NavLink to="/services" className={({ isActive }) =>
              `text-sm font-semibold transition-colors px-1 py-1 ${isActive ? 'text-white border-b-2 border-white' : 'text-white hover:text-white/80'}`
            }>
              Services
            </NavLink>
            <NavLink to="/afh" className={({ isActive }) =>
              `text-sm font-semibold transition-colors px-1 py-1 ${isActive ? 'text-white border-b-2 border-white' : 'text-white hover:text-white/80'}`
            }>
              Adult Family Home
            </NavLink>
            <NavLink to="/about" className={({ isActive }) =>
              `text-sm font-semibold transition-colors px-1 py-1 ${isActive ? 'text-white border-b-2 border-white' : 'text-white hover:text-white/80'}`
            }>
              About
            </NavLink>
            <Link to="/contact" className="btn bg-white text-primary hover:bg-white/90 text-sm py-1">
              Contact Us
            </Link>
          </nav>

          {/* Mobile Menu Button */}
          <button
            className="lg:hidden p-2 text-white hover:bg-white/10 transition-colors duration-200"
            onClick={toggleMenu}
            aria-expanded={isMenuOpen}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </header>

      {/* Mobile Navigation - Moved outside header to prevent containment issues */}
      <div className={`mobile-menu-overlay bg-white transform transition-transform duration-300 ease-in-out ${isMenuOpen ? 'translate-x-0' : 'translate-x-full'} lg:hidden`}>
        <div className="flex justify-end p-4 pt-6 bg-white border-b border-gray-100">
          <button
            className="p-2 text-primary hover:bg-primary/5 transition-colors duration-200"
            onClick={toggleMenu}
            aria-label="Close menu"
          >
            <X size={24} />
          </button>
        </div>
        <nav className="flex flex-col items-center justify-start space-y-8 p-6 pt-12 min-h-screen bg-white">
          <NavLink
            to="/"
            className={({ isActive }) => `text-xl font-semibold py-2 px-4 rounded-lg transition-colors ${isActive ? 'text-accent bg-accent/10' : 'text-primary hover:bg-primary/5'}`}
            onClick={toggleMenu}
          >
            Home
          </NavLink>
          <NavLink
            to="/services"
            className={({ isActive }) => `text-xl font-semibold py-2 px-4 rounded-lg transition-colors ${isActive ? 'text-accent bg-accent/10' : 'text-primary hover:bg-primary/5'}`}
            onClick={toggleMenu}
          >
            Services
          </NavLink>
          <NavLink
            to="/afh"
            className={({ isActive }) => `text-xl font-semibold py-2 px-4 rounded-lg transition-colors ${isActive ? 'text-accent bg-accent/10' : 'text-primary hover:bg-primary/5'}`}
            onClick={toggleMenu}
          >
            Adult Family Home
          </NavLink>
          <NavLink
            to="/about"
            className={({ isActive }) => `text-xl font-semibold py-2 px-4 rounded-lg transition-colors ${isActive ? 'text-accent bg-accent/10' : 'text-primary hover:bg-primary/5'}`}
            onClick={toggleMenu}
          >
            About
          </NavLink>

          <Link
            to="/contact"
            className="btn-primary mt-4"
            onClick={toggleMenu}
          >
            Contact Us
          </Link>
        </nav>
      </div>
    </>
  );
};

export default Header;
